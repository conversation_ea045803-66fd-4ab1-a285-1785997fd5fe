class Ball {
    constructor(x, y, radius = 8) {
        this.x = x;
        this.y = y;
        this.radius = radius;
        this.speed = 7;
        this.dx = 0;
        this.dy = 0;
        this.isLaunched = false;
        this.minBounceAngle = Math.PI / 8;
        this.trail = [];
        this.maxTrailLength = 5;
    }

    update(canvas, paddle) {
        // 更新拖尾效果
        if (this.isLaunched) {
            this.trail.unshift({ x: this.x, y: this.y });
            if (this.trail.length > this.maxTrailLength) {
                this.trail.pop();
            }
        } else {
            this.trail = [];
        }

        if (!this.isLaunched) {
            this.x = paddle.x + paddle.width / 2;
            this.y = paddle.y - this.radius;
            return;
        }

        // 保存原始位置用于碰撞检测
        const prevX = this.x;
        const prevY = this.y;

        this.x += this.dx;
        this.y += this.dy;

        // 墙壁碰撞
        let collision = false;
        
        // 左右墙壁碰撞
        if (this.x - this.radius < 0) {
            this.x = this.radius;
            this.dx = Math.abs(this.dx);
            collision = true;
        } else if (this.x + this.radius > canvas.width) {
            this.x = canvas.width - this.radius;
            this.dx = -Math.abs(this.dx);
            collision = true;
        }

        // 顶部碰撞
        if (this.y - this.radius < 0) {
            this.y = this.radius;
            this.dy = Math.abs(this.dy);
            
            // 确保垂直速度足够大
            const minVerticalSpeed = this.speed * 0.5;
            if (this.dy < minVerticalSpeed) {
                this.dy = minVerticalSpeed;
                
                // 调整水平速度以保持总速度不变
                const maxHorizontalSpeed = Math.sqrt(this.speed * this.speed - this.dy * this.dy);
                if (Math.abs(this.dx) > maxHorizontalSpeed) {
                    this.dx = this.dx > 0 ? maxHorizontalSpeed : -maxHorizontalSpeed;
                }
            }
            
            collision = true;
        }

        // 如果发生碰撞，确保总速度保持不变
        if (collision) {
            const currentSpeed = Math.sqrt(this.dx * this.dx + this.dy * this.dy);
            if (Math.abs(currentSpeed - this.speed) > 0.1) {
                const factor = this.speed / currentSpeed;
                this.dx *= factor;
                this.dy *= factor;
            }
        }

        // 挡板碰撞
        if (this.y + this.radius > paddle.y && 
            this.x > paddle.x && 
            this.x < paddle.x + paddle.width &&
            this.dy > 0) {
            
            const hitPoint = (this.x - paddle.x) / paddle.width;
            let angle;
            
            if (hitPoint < 0.2) {
                angle = -Math.PI/3;
            } else if (hitPoint > 0.8) {
                angle = -2*Math.PI/3;
            } else {
                const normalizedHit = (hitPoint - 0.5) * 2;
                angle = normalizedHit * (Math.PI/2 - this.minBounceAngle) - Math.PI/2;
            }

            this.dx = this.speed * Math.cos(angle);
            this.dy = this.speed * Math.sin(angle);

            const minSpeedY = this.speed * Math.sin(this.minBounceAngle);
            if (Math.abs(this.dy) < minSpeedY) {
                this.dy = -minSpeedY;
                // 调整水平速度以保持总速度不变
                const maxSpeedX = Math.sqrt(this.speed * this.speed - minSpeedY * minSpeedY);
                this.dx = this.dx > 0 ? maxSpeedX : -maxSpeedX;
            }
        }
    }

    draw(ctx) {
        // 绘制拖尾
        this.trail.forEach((pos, index) => {
            const alpha = (1 - index / this.maxTrailLength) * 0.3;
            ctx.beginPath();
            ctx.arc(pos.x, pos.y, this.radius * (1 - index / this.maxTrailLength), 0, Math.PI * 2);
            ctx.fillStyle = `rgba(129, 212, 250, ${alpha})`;
            ctx.fill();
        });

        // 绘制主球体
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
        
        // 创建径向渐变
        const gradient = ctx.createRadialGradient(
            this.x - this.radius/3, this.y - this.radius/3, 0,
            this.x, this.y, this.radius
        );
        gradient.addColorStop(0, '#fff');
        gradient.addColorStop(1, '#81d4fa');
        
        ctx.fillStyle = gradient;
        ctx.fill();
        
        // 添加光晕效果
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius + 2, 0, Math.PI * 2);
        ctx.strokeStyle = 'rgba(129, 212, 250, 0.3)';
        ctx.lineWidth = 2;
        ctx.stroke();
    }

    launch() {
        if (!this.isLaunched) {
            this.isLaunched = true;
            this.dx = 0;
            this.dy = -this.speed;
        }
    }

    reset(x, y) {
        this.x = x;
        this.y = y;
        this.dx = 0;
        this.dy = 0;
        this.isLaunched = false;
    }
}

class Paddle {
    constructor(canvas) {
        this.width = 100;
        this.height = 15;
        this.x = (canvas.width - this.width) / 2;
        this.y = canvas.height - 30;
        this.speed = 12;
        this.maxSpeed = 16;
        this.acceleration = 1.5;
        this.currentSpeed = this.speed;
        this.moveDirection = 0;
        this.canvas = canvas;
        this.smoothing = 0.8;
        this.targetX = this.x;
    }

    move(direction) {
        this.moveDirection = direction === 'left' ? -1 : direction === 'right' ? 1 : 0;
        
        if (this.moveDirection !== 0) {
            this.currentSpeed = Math.min(this.currentSpeed + this.acceleration, this.maxSpeed);
        } else {
            this.currentSpeed = this.speed;
        }

        const newX = this.x + this.moveDirection * this.currentSpeed;
        this.x = Math.max(0, Math.min(this.canvas.width - this.width, newX));
    }

    moveTo(targetX) {
        this.targetX = targetX - this.width / 2;
        
        const dx = this.targetX - this.x;
        this.x += dx * this.smoothing;
        
        this.x = Math.max(0, Math.min(this.canvas.width - this.width, this.x));
    }

    draw(ctx) {
        ctx.fillStyle = '#4fc3f7';
        ctx.fillRect(this.x, this.y, this.width, this.height);
        
        const gradient = ctx.createLinearGradient(this.x, this.y, this.x, this.y + this.height);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        ctx.fillStyle = gradient;
        ctx.fillRect(this.x, this.y, this.width, this.height);
    }
}

class Brick {
    constructor(x, y, width, height, color, points = 10, hitPoints = 1) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.color = color;
        this.points = points;
        this.broken = false;
        this.breakAnimation = 0;
        this.isBreaking = false;
        this.hitPoints = hitPoints;
        this.maxHitPoints = hitPoints;
        this.originalColor = color;
        this.hitFlash = 0;
        this.shakeOffset = { x: 0, y: 0 };
    }

    draw(ctx) {
        if (this.broken && !this.isBreaking) return;

        const drawX = this.x + this.shakeOffset.x;
        const drawY = this.y + this.shakeOffset.y;

        if (this.isBreaking) {
            this.breakAnimation += 0.1;
            ctx.globalAlpha = 1 - this.breakAnimation;
            if (this.breakAnimation >= 1) {
                this.isBreaking = false;
                ctx.globalAlpha = 1;
                return;
            }
        }

        if (this.hitPoints < this.maxHitPoints) {
            const ratio = this.hitPoints / this.maxHitPoints;
            const rgb = this.hexToRgb(this.originalColor);
            const darkenedColor = `rgb(${Math.floor(rgb.r * ratio)}, ${Math.floor(rgb.g * ratio)}, ${Math.floor(rgb.b * ratio)})`;
            ctx.fillStyle = darkenedColor;
        } else {
            ctx.fillStyle = this.color;
        }

        ctx.fillRect(drawX, drawY, this.width, this.height);

        if (this.hitFlash > 0) {
            ctx.fillStyle = `rgba(255, 255, 255, ${this.hitFlash})`;
            ctx.fillRect(drawX, drawY, this.width, this.height);
            this.hitFlash = Math.max(0, this.hitFlash - 0.1);
        }

        const gradient = ctx.createLinearGradient(drawX, drawY, drawX, drawY + this.height);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        ctx.fillStyle = gradient;
        ctx.fillRect(drawX, drawY, this.width, this.height);

        ctx.strokeStyle = 'rgba(0, 0, 0, 0.2)';
        ctx.lineWidth = 1;
        ctx.strokeRect(drawX, drawY, this.width, this.height);
        
        if (this.isBreaking) {
            ctx.globalAlpha = 1;
        }

        if (this.maxHitPoints > 1 && this.hitPoints > 0) {
            ctx.fillStyle = '#fff';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(this.hitPoints, drawX + this.width / 2, drawY + this.height / 2);
        }

        if (this.shakeOffset.x !== 0 || this.shakeOffset.y !== 0) {
            this.shakeOffset.x *= 0.8;
            this.shakeOffset.y *= 0.8;
            if (Math.abs(this.shakeOffset.x) < 0.1) this.shakeOffset.x = 0;
            if (Math.abs(this.shakeOffset.y) < 0.1) this.shakeOffset.y = 0;
        }
    }

    break() {
        this.hitPoints--;
        this.hitFlash = 0.8;
        this.shakeOffset = {
            x: (Math.random() - 0.5) * 4,
            y: (Math.random() - 0.5) * 4
        };
        
        if (this.hitPoints <= 0) {
            this.broken = true;
            this.isBreaking = true;
            this.breakAnimation = 0;
            return this.points;
        }
        return 0;
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    checkCollision(ball) {
        if (this.broken) return false;

        const ballLeft = ball.x - ball.radius;
        const ballRight = ball.x + ball.radius;
        const ballTop = ball.y - ball.radius;
        const ballBottom = ball.y + ball.radius;

        const brickLeft = this.x;
        const brickRight = this.x + this.width;
        const brickTop = this.y;
        const brickBottom = this.y + this.height;

        if (ballRight >= brickLeft && 
            ballLeft <= brickRight && 
            ballBottom >= brickTop && 
            ballTop <= brickBottom) {
            
            const fromLeft = Math.abs(ballRight - brickLeft);
            const fromRight = Math.abs(brickRight - ballLeft);
            const fromTop = Math.abs(ballBottom - brickTop);
            const fromBottom = Math.abs(brickBottom - ballTop);

            const minOverlap = Math.min(fromLeft, fromRight, fromTop, fromBottom);

            if (minOverlap === fromLeft || minOverlap === fromRight) {
                ball.dx = -ball.dx;
                if (Math.abs(ball.dx) < 2) {
                    ball.dx = ball.dx > 0 ? 2 : -2;
                }
            } else {
                ball.dy = -ball.dy;
                if (Math.abs(ball.dy) < 2) {
                    ball.dy = ball.dy > 0 ? 2 : -2;
                }
                if (Math.abs(ball.dx) > Math.abs(ball.dy) * 2) {
                    ball.dx = ball.dx > 0 ? Math.abs(ball.dy) : -Math.abs(ball.dy);
                }
            }

            const points = this.break();
            return points > 0;
        }
        return false;
    }
}

class Particle {
    constructor(x, y, color, speed = 5) {
        this.x = x;
        this.y = y;
        this.color = color;
        this.size = Math.random() * 3 + 2;
        this.speedX = (Math.random() - 0.5) * speed;
        this.speedY = (Math.random() - 0.5) * speed;
        this.gravity = 0.1;
        this.life = 1.0;
        this.decay = Math.random() * 0.02 + 0.02;
    }

    update() {
        this.x += this.speedX;
        this.y += this.speedY;
        this.speedY += this.gravity;
        this.life -= this.decay;
        return this.life > 0;
    }

    draw(ctx) {
        ctx.globalAlpha = this.life;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.globalAlpha = 1;
    }
}

class ParticleSystem {
    constructor() {
        this.particles = [];
    }

    createExplosion(x, y, color, count = 15) {
        for (let i = 0; i < count; i++) {
            const angle = (Math.PI * 2 * i) / count;
            const speed = 2 + Math.random() * 3;
            const particle = {
                x: x,
                y: y,
                dx: Math.cos(angle) * speed,
                dy: Math.sin(angle) * speed,
                alpha: 1,
                color: color,
                size: 2 + Math.random() * 3
            };
            this.particles.push(particle);
        }
    }

    update() {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            particle.x += particle.dx;
            particle.y += particle.dy;
            particle.alpha *= 0.95;
            if (particle.alpha < 0.1) {
                this.particles.splice(i, 1);
            }
        }
    }

    draw(ctx) {
        this.particles.forEach(particle => {
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(${this.hexToRgb(particle.color).join(',')},${particle.alpha})`;
            ctx.fill();
        });
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? [
            parseInt(result[1], 16),
            parseInt(result[2], 16),
            parseInt(result[3], 16)
        ] : [255, 255, 255];
    }
}

class RippleEffect {
    constructor() {
        this.ripples = [];
    }

    createRipple(x, y) {
        this.ripples.push({
            x: x,
            y: y,
            radius: 0,
            maxRadius: 30,
            alpha: 1
        });
    }

    update() {
        for (let i = this.ripples.length - 1; i >= 0; i--) {
            const ripple = this.ripples[i];
            ripple.radius += 2;
            ripple.alpha *= 0.95;
            if (ripple.alpha < 0.1) {
                this.ripples.splice(i, 1);
            }
        }
    }

    draw(ctx) {
        this.ripples.forEach(ripple => {
            ctx.beginPath();
            ctx.arc(ripple.x, ripple.y, ripple.radius, 0, Math.PI * 2);
            ctx.strokeStyle = `rgba(255, 255, 255, ${ripple.alpha})`;
            ctx.lineWidth = 2;
            ctx.stroke();
        });
    }
}

class HitEffect {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.radius = 5;
        this.maxRadius = 20;
        this.alpha = 1;
        this.decay = 0.1;
    }

    update() {
        this.radius += 2;
        this.alpha -= this.decay;
        return this.alpha > 0;
    }

    draw(ctx) {
        ctx.globalAlpha = this.alpha;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 2;
        ctx.stroke();
        ctx.globalAlpha = 1;
    }
}

class ScoreAnimation {
    constructor() {
        this.animations = [];
    }

    addScore(x, y, points) {
        this.animations.push({
            x: x,
            y: y,
            points: `+${points}`,
            alpha: 1,
            scale: 1
        });
    }

    update() {
        for (let i = this.animations.length - 1; i >= 0; i--) {
            const anim = this.animations[i];
            anim.y -= 2;
            anim.alpha *= 0.95;
            anim.scale += 0.05;
            if (anim.alpha < 0.1) {
                this.animations.splice(i, 1);
            }
        }
    }

    draw(ctx) {
        this.animations.forEach(anim => {
            ctx.save();
            ctx.globalAlpha = anim.alpha;
            ctx.font = `${20 * anim.scale}px Arial`;
            ctx.fillStyle = '#ffeb3b';
            ctx.textAlign = 'center';
            ctx.fillText(anim.points, anim.x, anim.y);
            ctx.restore();
        });
    }
}

class LevelTransition {
    constructor() {
        this.active = false;
        this.progress = 0;
        this.text = '';
    }

    start(level) {
        this.active = true;
        this.progress = 0;
        this.text = `Level ${level}`;
    }

    update() {
        if (this.active) {
            this.progress += 0.02;
            if (this.progress >= 1) {
                this.active = false;
            }
        }
    }

    draw(ctx, canvas) {
        if (!this.active) return;

        ctx.save();
        
        ctx.fillStyle = `rgba(0, 0, 0, ${0.7 * Math.sin(this.progress * Math.PI)})`;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        const scale = 1 + Math.sin(this.progress * Math.PI) * 0.5;
        ctx.font = `${48 * scale}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        ctx.shadowColor = '#4fc3f7';
        ctx.shadowBlur = 20;
        ctx.fillStyle = '#fff';
        ctx.fillText(this.text, canvas.width / 2, canvas.height / 2);
        
        ctx.restore();
    }
}

class Game {
    static createInstance() {
        if (window.gameInstance) {
            console.warn('游戏实例已经存在');
            return window.gameInstance;
        }

        try {
            window.gameInstance = new Game();
            console.log('游戏实例创建成功');
            return window.gameInstance;
        } catch (error) {
            console.error('创建游戏实例失败:', error);
            const errorMessage = document.createElement('div');
            errorMessage.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255, 0, 0, 0.9);
                color: white;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
                z-index: 9999;
            `;
            errorMessage.innerHTML = `
                <h2>游戏初始化失败</h2>
                <p>${error.message}</p>
                <button onclick="location.reload()" style="
                    margin-top: 10px;
                    padding: 10px 20px;
                    background: white;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                ">重新加载</button>
            `;
            document.body.appendChild(errorMessage);
            throw error;
        }
    }

    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        if (!this.canvas) {
            throw new Error('Canvas element not found');
        }

        this.ctx = this.canvas.getContext('2d');
        if (!this.ctx) {
            throw new Error('Failed to get canvas context');
        }
        
        const requiredElements = [
            { id: 'score', name: '分数显示' },
            { id: 'level', name: '关卡显示' },
            { id: 'lives', name: '生命显示' },
            { id: 'bigScore', name: '大分数显示' },
            { id: 'startBtn', name: '开始按钮' },
            { id: 'resetBtn', name: '重置按钮' },
            { id: 'selectLevelBtn', name: '选择关卡按钮' },
            { id: 'overlay', name: '遮罩层' },
            { id: 'levelSelect', name: '关卡选择面板' },
            { id: 'levelGrid', name: '关卡网格' }
        ];
        
        const missingElements = requiredElements.filter(({id, name}) => {
            const element = document.getElementById(id);
            if (!element) {
                console.error(`找不到${name} (ID: ${id})`);
                return true;
            }
            return false;
        });

        if (missingElements.length > 0) {
            throw new Error(`缺少必要的DOM元素: ${missingElements.map(e => e.name).join(', ')}`);
        }

        this.score = 0;
        this.level = 1;
        this.lives = 3;
        this.isGameOver = false;
        this.isPlaying = false;
        this.combo = 0;
        this.comboTimer = 0;
        this.lastBrickHitTime = 0;
        this.maxUnlockedLevel = parseInt(localStorage.getItem('maxUnlockedLevel')) || 1;

        this.paddle = new Paddle(this.canvas);
        this.ball = new Ball(
            this.paddle.x + this.paddle.width / 2,
            this.paddle.y - 10
        );
        this.bricks = [];
        this.particleSystem = new ParticleSystem();
        this.rippleEffect = new RippleEffect();
        this.hitEffects = [];
        this.scoreAnimation = new ScoreAnimation();
        this.levelTransition = new LevelTransition();

        this.keyState = {
            left: false,
            right: false
        };

        this.start = this.start.bind(this);
        this.reset = this.reset.bind(this);
        this.updateLevelGrid = this.updateLevelGrid.bind(this);
        this.animate = this.animate.bind(this);

        try {
            this.bricks = this.createBricks();
            this.setupEventListeners();
            this.setupLevelSelect();
            this.updateScore(0);
            this.updateLives(this.lives);
            this.updateLevel(this.level);

            if (!this.animationFrameId) {
                this.animate();
            }
            
            console.log('Game initialized successfully');
        } catch (error) {
            console.error('Error during game initialization:', error);
        }
    }

    setupLevelSelect() {
        const overlay = document.getElementById('overlay');
        const levelSelect = document.getElementById('levelSelect');
        const levelGrid = document.getElementById('levelGrid');
        const selectLevelBtn = document.getElementById('selectLevelBtn');
        const closeLevelSelect = document.getElementById('closeLevelSelect');

        this.updateLevelGrid();

        selectLevelBtn.addEventListener('click', () => {
            overlay.classList.add('active');
            levelSelect.classList.add('active');
            this.updateLevelGrid();
        });

        closeLevelSelect.addEventListener('click', () => {
            overlay.classList.remove('active');
            levelSelect.classList.remove('active');
        });

        overlay.addEventListener('click', () => {
            overlay.classList.remove('active');
            levelSelect.classList.remove('active');
        });
    }

    updateLevelGrid() {
        const levelGrid = document.getElementById('levelGrid');
        levelGrid.innerHTML = '';

        for (let i = 1; i <= 30; i++) {
            const button = document.createElement('button');
            button.className = 'level-btn';
            button.textContent = i;

            if (i <= this.maxUnlockedLevel) {
                button.addEventListener('click', () => {
                    this.selectLevel(i);
                });
            } else {
                button.disabled = true;
                button.style.opacity = '0.5';
            }

            levelGrid.appendChild(button);
        }
    }

    selectLevel(level) {
        if (level > 0) {
            this.level = level;
            this.reset(true);
            this.start();
            document.getElementById('levelSelect').classList.remove('active');
            document.getElementById('overlay').classList.remove('active');
        }
    }

    createBricks() {
        const brickWidth = 60;
        const brickHeight = 20;
        const padding = 10;
        const topOffset = 50;

        const colorThemes = [
            ['#f44336', '#e91e63', '#9c27b0', '#673ab7', '#3f51b5'],
            ['#4caf50', '#8bc34a', '#cddc39', '#ffeb3b', '#ffc107'],
            ['#00bcd4', '#03a9f4', '#2196f3', '#3f51b5', '#673ab7'],
            ['#ff5722', '#ff9800', '#ffc107', '#ffeb3b', '#cddc39'],
            ['#9c27b0', '#e91e63', '#f44336', '#ff5722', '#ff9800']
        ];

        const themeIndex = (this.level - 1) % colorThemes.length;
        const colors = colorThemes[themeIndex];

        const layouts = [
            this.createClassicLayout.bind(this),
            this.createArcLayout.bind(this),
            this.createDiamondLayout.bind(this),
            this.createHeartLayout.bind(this),
            this.createSpaceInvaderLayout.bind(this),
            this.createMazeLayout.bind(this),
            this.createZigzagLayout.bind(this),
            this.createPyramidLayout.bind(this),
            this.createSpiralLayout.bind(this),
            this.createRandomLayout.bind(this)
        ];

        const layoutIndex = (this.level - 1) % layouts.length;
        const bricks = layouts[layoutIndex](brickWidth, brickHeight, padding, topOffset, colors);

        if (this.level > 1) {
            const baseHealth = 1;
            const maxExtraHealth = Math.min(2, Math.floor((this.level - 1) / 3));
            
            bricks.forEach(brick => {
                if (Math.random() < 0.2) {
                    const extraHealth = Math.floor(Math.random() * (maxExtraHealth + 1));
                    brick.hitPoints = baseHealth + extraHealth;
                    brick.maxHitPoints = brick.hitPoints;
                    
                    brick.points *= (1 + extraHealth);
                }
            });
        }

        return bricks;
    }

    createClassicLayout(brickWidth, brickHeight, padding, topOffset, colors) {
        const bricks = [];
        const cols = Math.floor(this.canvas.width / (brickWidth + padding));
        const sideOffset = (this.canvas.width - (cols * (brickWidth + padding))) / 2;
        const rows = 5;

        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                const x = col * (brickWidth + padding) + sideOffset;
                const y = row * (brickHeight + padding) + topOffset;
                const hitPoints = Math.floor(Math.random() * this.level * 0.5) + 1;
                bricks.push(new Brick(
                    x, y, brickWidth, brickHeight,
                    colors[row % colors.length],
                    (rows - row) * 10 * hitPoints,
                    hitPoints
                ));
            }
        }
        return bricks;
    }

    createArcLayout(brickWidth, brickHeight, padding, topOffset, colors) {
        const bricks = [];
        const centerX = this.canvas.width / 2;
        const radius = this.canvas.width / 3;
        const bricksInCircle = 16;
        const circles = 4;

        for (let circle = 0; circle < circles; circle++) {
            const currentRadius = radius - circle * (brickHeight + padding);
            for (let i = 0; i < bricksInCircle; i++) {
                const angle = (i / bricksInCircle) * Math.PI * 2;
                const x = centerX + Math.cos(angle) * currentRadius - brickWidth / 2;
                const y = topOffset + 100 + Math.sin(angle) * currentRadius - brickHeight / 2;
                const hitPoints = Math.floor(Math.random() * this.level * 0.3) + 1;
                bricks.push(new Brick(
                    x, y, brickWidth, brickHeight,
                    colors[circle % colors.length],
                    (circles - circle) * 20 * hitPoints,
                    hitPoints
                ));
            }
        }
        return bricks;
    }

    createDiamondLayout(brickWidth, brickHeight, padding, topOffset, colors) {
        const bricks = [];
        const centerX = (this.canvas.width - brickWidth) / 2;
        const size = 7;

        for (let i = 0; i < size; i++) {
            for (let j = 0; j < size; j++) {
                if (Math.abs(i - size/2) + Math.abs(j - size/2) <= size/2) {
                    const x = centerX + (j - size/2) * (brickWidth + padding);
                    const y = topOffset + i * (brickHeight + padding);
                    const colorIndex = Math.floor(Math.random() * colors.length);
                    bricks.push(new Brick(x, y, brickWidth, brickHeight, colors[colorIndex]));
                }
            }
        }
        return bricks;
    }

    createHeartLayout(brickWidth, brickHeight, padding, topOffset, colors) {
        const bricks = [];
        const centerX = (this.canvas.width - brickWidth) / 2;
        const heartShape = [
            "  ***   *** ",
            " ********** ",
            " ********** ",
            "  ********  ",
            "   ******   ",
            "    ****    ",
            "     **     "
        ];

        heartShape.forEach((row, i) => {
            for (let j = 0; j < row.length; j++) {
                if (row[j] === '*') {
                    const x = centerX + (j - row.length/2) * (brickWidth + padding);
                    const y = topOffset + i * (brickHeight + padding);
                    const colorIndex = Math.floor(Math.random() * colors.length);
                    bricks.push(new Brick(x, y, brickWidth, brickHeight, colors[colorIndex]));
                }
            }
        });
        return bricks;
    }

    createSpaceInvaderLayout(brickWidth, brickHeight, padding, topOffset, colors) {
        const bricks = [];
        const centerX = (this.canvas.width - brickWidth) / 2;
        const invaderShape = [
            "   **  **   ",
            "    ****    ",
            "   ******   ",
            "  ** ** **  ",
            " ********** ",
            "  * **** *  ",
            " * *    * * "
        ];

        invaderShape.forEach((row, i) => {
            for (let j = 0; j < row.length; j++) {
                if (row[j] === '*') {
                    const x = centerX + (j - row.length/2) * (brickWidth + padding);
                    const y = topOffset + i * (brickHeight + padding);
                    const colorIndex = Math.floor(Math.random() * colors.length);
                    bricks.push(new Brick(x, y, brickWidth, brickHeight, colors[colorIndex]));
                }
            }
        });
        return bricks;
    }

    createMazeLayout(brickWidth, brickHeight, padding, topOffset, colors) {
        const bricks = [];
        const cols = Math.floor(this.canvas.width / (brickWidth + padding));
        const rows = 8;
        const sideOffset = (this.canvas.width - (cols * (brickWidth + padding))) / 2;

        const maze = Array(rows).fill().map(() => Array(cols).fill(1));
        const path = [[0, Math.floor(cols/2)]];
        maze[0][Math.floor(cols/2)] = 0;

        while (path.length > 0) {
            const current = path[path.length - 1];
            const directions = [
                [0, 1], [1, 0], [0, -1], [-1, 0]
            ].sort(() => Math.random() - 0.5);

            let moved = false;
            for (const [dy, dx] of directions) {
                const newY = current[0] + dy * 2;
                const newX = current[1] + dx * 2;

                if (newY >= 0 && newY < rows && newX >= 0 && newX < cols && maze[newY][newX] === 1) {
                    maze[current[0] + dy][current[1] + dx] = 0;
                    maze[newY][newX] = 0;
                    path.push([newY, newX]);
                    moved = true;
                    break;
                }
            }

            if (!moved) {
                path.pop();
            }
        }

        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                if (maze[row][col] === 1) {
                    const x = col * (brickWidth + padding) + sideOffset;
                    const y = row * (brickHeight + padding) + topOffset;
                    const hitPoints = Math.floor(Math.random() * this.level * 0.3) + 1;
                    bricks.push(new Brick(
                        x, y, brickWidth, brickHeight,
                        colors[row % colors.length],
                        (rows - row) * 25 * hitPoints,
                        hitPoints
                    ));
                }
            }
        }
        return bricks;
    }

    createPyramidLayout(brickWidth, brickHeight, padding, topOffset, colors) {
        const bricks = [];
        const rows = 6;
        const maxBricksInRow = 11;

        for (let row = 0; row < rows; row++) {
            const bricksInThisRow = maxBricksInRow - (row * 2);
            const startX = (this.canvas.width - (bricksInThisRow * (brickWidth + padding))) / 2;

            for (let col = 0; col < bricksInThisRow; col++) {
                const x = startX + col * (brickWidth + padding);
                const y = topOffset + row * (brickHeight + padding);
                const colorIndex = Math.floor(Math.random() * colors.length);
                bricks.push(new Brick(x, y, brickWidth, brickHeight, colors[colorIndex]));
            }
        }
        return bricks;
    }

    createSpiralLayout(brickWidth, brickHeight, padding, topOffset, colors) {
        const bricks = [];
        const size = 9;
        const centerX = (this.canvas.width - brickWidth) / 2;
        const centerY = topOffset + (size * (brickHeight + padding)) / 2;

        let x = 0, y = 0;
        let dx = 1, dy = 0;
        let steps = size - 1;
        let stepCount = 0;

        for (let i = 0; i < size * size; i++) {
            if (stepCount === steps) {
                stepCount = 0;
                const temp = dx;
                dx = -dy;
                dy = temp;
                if (dy === 0) steps--;
            }

            const brickX = centerX + (x - size/2) * (brickWidth + padding);
            const brickY = centerY + (y - size/2) * (brickHeight + padding);
            
            if (brickX >= 0 && brickX + brickWidth <= this.canvas.width &&
                brickY >= topOffset && brickY + brickHeight <= this.canvas.height) {
                const colorIndex = Math.floor(Math.random() * colors.length);
                bricks.push(new Brick(brickX, brickY, brickWidth, brickHeight, colors[colorIndex]));
            }

            x += dx;
            y += dy;
            stepCount++;
        }
        return bricks;
    }

    createZigzagLayout(brickWidth, brickHeight, padding, topOffset, colors) {
        const bricks = [];
        const cols = Math.floor(this.canvas.width / (brickWidth + padding));
        const rows = 8;
        const sideOffset = (this.canvas.width - (cols * (brickWidth + padding))) / 2;

        for (let row = 0; row < rows; row++) {
            const offset = (row % 2) * ((brickWidth + padding) / 2);
            for (let col = 0; col < cols - (row % 2); col++) {
                const x = col * (brickWidth + padding) + sideOffset + offset;
                const y = row * (brickHeight + padding) + topOffset;
                const colorIndex = Math.floor(Math.random() * colors.length);
                const hitPoints = Math.floor(Math.random() * this.level * 0.3) + 1;
                bricks.push(new Brick(
                    x, y, brickWidth, brickHeight,
                    colors[colorIndex],
                    (rows - row) * 15 * hitPoints,
                    hitPoints
                ));
            }
        }
        return bricks;
    }

    createRandomLayout(brickWidth, brickHeight, padding, topOffset, colors) {
        const bricks = [];
        const cols = Math.floor(this.canvas.width / (brickWidth + padding));
        const rows = 8;
        const sideOffset = (this.canvas.width - (cols * (brickWidth + padding))) / 2;
        const density = 0.7;

        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                if (Math.random() < density) {
                    const x = col * (brickWidth + padding) + sideOffset;
                    const y = row * (brickHeight + padding) + topOffset;
                    const colorIndex = Math.floor(Math.random() * colors.length);
                    const hitPoints = Math.floor(Math.random() * this.level * 0.3) + 1;
                    bricks.push(new Brick(
                        x, y, brickWidth, brickHeight,
                        colors[colorIndex],
                        (rows - row) * 20 * hitPoints,
                        hitPoints
                    ));
                }
            }
        }
        return bricks;
    }

    setupEventListeners() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                this.keyState.left = true;
                e.preventDefault();
            } else if (e.key === 'ArrowRight') {
                this.keyState.right = true;
                e.preventDefault();
            } else if (e.key === ' ' || e.key === 'Enter') {
                if (this.isPlaying) {
                    this.ball.launch();
                }
                e.preventDefault();
            }
        });

        document.addEventListener('keyup', (e) => {
            if (e.key === 'ArrowLeft') {
                this.keyState.left = false;
            } else if (e.key === 'ArrowRight') {
                this.keyState.right = false;
            }
        });

        this.canvas.addEventListener('mousemove', (e) => {
            if (this.isPlaying) {
                const rect = this.canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                this.paddle.moveTo(x);
            }
        });

        this.canvas.addEventListener('click', () => {
            if (this.isPlaying && !this.isGameOver) {
                this.ball.launch();
            }
        });

        const startBtn = document.getElementById('startBtn');
        const resetBtn = document.getElementById('resetBtn');
        const selectLevelBtn = document.getElementById('selectLevelBtn');

        startBtn.addEventListener('click', () => {
            if (!this.isPlaying) {
                this.start();
                startBtn.blur();
            }
        });

        resetBtn.addEventListener('click', () => {
            this.reset();
            this.start();
            resetBtn.blur();
        });

        selectLevelBtn.addEventListener('click', () => {
            const overlay = document.getElementById('overlay');
            const levelSelect = document.getElementById('levelSelect');
            
            overlay.classList.add('active');
            levelSelect.classList.add('active');
            this.updateLevelGrid();
            selectLevelBtn.blur();
        });

        const closeLevelSelect = document.getElementById('closeLevelSelect');
        closeLevelSelect.addEventListener('click', () => {
            const overlay = document.getElementById('overlay');
            const levelSelect = document.getElementById('levelSelect');
            overlay.classList.remove('active');
            levelSelect.classList.remove('active');
        });

        const overlay = document.getElementById('overlay');
        overlay.addEventListener('click', () => {
            overlay.classList.remove('active');
            document.getElementById('levelSelect').classList.remove('active');
        });
    }

    start() {
        if (this.isPlaying) {
            return;
        }

        this.isPlaying = true;
        this.isGameOver = false;
        
        this.score = 0;
        this.combo = 0;
        this.lives = 3;
        
        this.updateScore(0);
        this.updateLives(this.lives);
        this.updateLevel(this.level);
        
        this.paddle = new Paddle(this.canvas);
        this.ball = new Ball(
            this.paddle.x + this.paddle.width / 2,
            this.paddle.y - 10
        );
        this.bricks = this.createBricks();
        
        if (!this.animationFrameId) {
            this.animate();
        }

        console.log('Game started successfully');
    }

    animate() {
        if (this.isPlaying) {
            this.update();
            this.draw();
            this.animationFrameId = requestAnimationFrame(() => this.animate());
        }
    }

    reset(keepLevel = false) {
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }

        this.isPlaying = false;
        this.isGameOver = false;
        this.score = 0;
        this.combo = 0;
        this.lives = 3;
        
        if (!keepLevel) {
            this.level = 1;
        }
        
        this.updateScore(0);
        this.updateLives(this.lives);
        this.updateLevel(this.level);
        
        this.paddle = new Paddle(this.canvas);
        this.ball = new Ball(
            this.paddle.x + this.paddle.width / 2,
            this.paddle.y - 10
        );
        this.bricks = this.createBricks();

        console.log('Game reset successfully');
    }

    nextLevel() {
        this.level++;
        this.updateLevel(this.level);
        this.ball.reset(
            this.paddle.x + this.paddle.width / 2,
            this.paddle.y - 10
        );
        this.bricks = this.createBricks();
        this.levelTransition.start(this.level);
    }

    updateScore(points) {
        const now = Date.now();
        if (points > 0) {
            if (now - this.lastBrickHitTime < 1000) {
                this.combo++;
                this.comboTimer = 60;
                points = Math.floor(points * (1 + this.combo * 0.5));
            } else {
                this.combo = 0;
            }
            this.lastBrickHitTime = now;
        }

        this.score += points;
        document.getElementById('score').textContent = this.score;
        document.getElementById('bigScore').textContent = this.score;
        
        this.scoreAnimation.addScore(
            this.ball.x,
            this.ball.y - 20,
            points
        );

        if (this.score > this.highScore) {
            this.highScore = this.score;
            localStorage.setItem('breakoutHighScore', this.highScore);
        }
    }

    updateLives(lives) {
        document.getElementById('lives').textContent = lives;
    }

    updateLevel(level) {
        document.getElementById('level').textContent = level;
    }

    update() {
        if (!this.isPlaying || this.isGameOver) return;

        if (this.keyState.left && !this.keyState.right) {
            this.paddle.move('left');
        } else if (this.keyState.right && !this.keyState.left) {
            this.paddle.move('right');
        } else if (!this.keyState.left && !this.keyState.right) {
            this.paddle.move('none');
        }

        this.ball.update(this.canvas, this.paddle);

        this.particleSystem.update();
        this.rippleEffect.update();
        this.hitEffects = this.hitEffects.filter(effect => effect.update());

        if (this.comboTimer > 0) {
            this.comboTimer--;
            if (this.comboTimer === 0) {
                this.combo = 0;
            }
        }

        let brickHit = false;
        let allBricksBroken = true;
        
        for (const brick of this.bricks) {
            if (!brick.broken) {
                allBricksBroken = false;
                if (brick.checkCollision(this.ball)) {
                    brickHit = true;
                    this.particleSystem.createExplosion(
                        brick.x + brick.width / 2,
                        brick.y + brick.height / 2,
                        brick.color
                    );
                    this.rippleEffect.createRipple(
                        brick.x + brick.width / 2,
                        brick.y + brick.height / 2
                    );

                    this.hitEffects.push(new HitEffect(this.ball.x, this.ball.y));
                    
                    const now = Date.now();
                    if (now - this.lastBrickHitTime < 1000) {
                        this.combo++;
                        this.comboTimer = 60;
                        this.updateScore(brick.points * (1 + this.combo * 0.5));
                    } else {
                        this.combo = 0;
                        this.updateScore(brick.points);
                    }
                    this.lastBrickHitTime = now;
                }
            }
        }

        if (allBricksBroken) {
            this.nextLevel();
            return;
        }

        if (this.ball.y > this.canvas.height + this.ball.radius) {
            this.lives--;
            this.updateLives(this.lives);
            
            if (this.lives <= 0) {
                this.isGameOver = true;
            } else {
                this.ball.reset(
                    this.paddle.x + this.paddle.width / 2,
                    this.paddle.y - 10
                );
            }
        }

        this.scoreAnimation.update();
        this.levelTransition.update();
    }

    draw() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        this.drawBackground();

        this.bricks.forEach(brick => brick.draw(this.ctx));
        this.paddle.draw(this.ctx);
        this.ball.draw(this.ctx);

        this.particleSystem.draw(this.ctx);
        this.rippleEffect.draw(this.ctx);
        this.hitEffects.forEach(effect => effect.draw(this.ctx));

        if (this.isGameOver) {
            this.drawGameOver();
        }

        if (this.combo > 0) {
            this.ctx.fillStyle = '#ffeb3b';
            this.ctx.font = 'bold 24px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(`${this.combo}x Combo!`, this.canvas.width / 2, 50);
        }

        this.scoreAnimation.draw(this.ctx);
        this.levelTransition.draw(this.ctx, this.canvas);
    }

    drawBackground() {
        const gradient = this.ctx.createLinearGradient(
            0, 0,
            0, this.canvas.height
        );
        gradient.addColorStop(0, '#1a237e');
        gradient.addColorStop(1, '#000051');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        for (let i = 0; i < 50; i++) {
            const x = Math.random() * this.canvas.width;
            const y = Math.random() * this.canvas.height;
            const size = Math.random() * 2;
            this.ctx.fillRect(x, y, size, size);
        }
    }

    drawGameOver() {
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        this.ctx.fillStyle = 'white';
        this.ctx.font = '48px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('游戏结束', this.canvas.width / 2, this.canvas.height / 2);
        
        this.ctx.font = '24px Arial';
        this.ctx.fillText(
            `最终得分: ${this.score}`,
            this.canvas.width / 2,
            this.canvas.height / 2 + 40
        );
        this.ctx.fillText(
            `达到等级: ${this.level}`,
            this.canvas.width / 2,
            this.canvas.height / 2 + 70
        );
    }
}

document.addEventListener('DOMContentLoaded', () => {
    Game.createInstance();
}); 